// ignore_for_file: use_build_context_synchronously

import 'dart:io';

import 'package:builders_konnect/core/core.dart';
import 'package:builders_konnect/ui/components/components.dart';

class EditDocumentsScreen extends ConsumerStatefulWidget {
  const EditDocumentsScreen({super.key, required this.documents});

  final Documents documents;

  @override
  ConsumerState<EditDocumentsScreen> createState() =>
      _EditDocumentsScreenState();
}

class _EditDocumentsScreenState extends ConsumerState<EditDocumentsScreen> {
  final formKey = GlobalKey<FormState>();
  final cacNumberC = TextEditingController();
  final tinNumberC = TextEditingController();

  File? _cacFile;
  File? _tinFile;
  File? _proofOfAddressFile;

  String? _cacUrl;
  String? _tinUrl;
  String? _proofOfAddressUrl;

  @override
  void initState() {
    cacNumberC.text = widget.documents.cac?.identifier ?? '';
    tinNumberC.text = widget.documents.tin?.identifier ?? '';

    _cacUrl = widget.documents.cac?.file;
    _tinUrl = widget.documents.tin?.file;
    _proofOfAddressUrl = widget.documents.proofOfAddress;

    super.initState();
  }

  @override
  void dispose() {
    cacNumberC.dispose();
    tinNumberC.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;
    final profileVm = ref.watch(vendorProfileVmodel);
    return BusyOverlay(
      show: profileVm.busy(updateState),
      child: Scaffold(
          appBar: CustomAppbar(
            title: "Edit Request",
          ),
          body: Form(
            key: formKey,
            child: ListView(
              padding: EdgeInsets.only(
                left: Sizer.width(16),
                right: Sizer.width(16),
                bottom: Sizer.height(50),
              ),
              children: [
                YBox(16),
                Container(
                  padding: EdgeInsets.all(Sizer.radius(16)),
                  decoration: BoxDecoration(
                    color: colorScheme.white,
                    borderRadius: BorderRadius.circular(Sizer.radius(4)),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text("Document Uploads", style: textTheme.text16?.medium),
                      Text(
                        "Edit information and submit for approval",
                        style: textTheme.text12?.copyWith(
                          color: colorScheme.black45,
                        ),
                      ),
                      YBox(16),
                      CustomTextField(
                        controller: cacNumberC,
                        labelText: 'CAC Number',
                        showLabelHeader: true,
                      ),
                      YBox(10),
                      UploadWidget(
                        labelText: 'Certificate:',
                        documentName: _cacUrl ?? _cacFile?.path.split('/').last,
                        isUploading:
                            ref.watch(fileUploadVm).busy("cacUploadState"),
                        onUpload: () async {
                          final file = await ImageAndDocUtils.pickDocument();
                          if (file != null) {
                            _cacFile = file;
                            final r = await ref.read(fileUploadVm).uploadFile(
                                file: [file], busyObjectName: "cacUploadState");
                            _cacUrl = r.data?.first.url;
                            setState(() {});
                          }
                        },
                        onRemove: () {
                          _cacFile = null;
                          _cacUrl = null;
                          setState(() {});
                        },
                      ),
                      // Column(
                      //   crossAxisAlignment: CrossAxisAlignment.start,
                      //   children: [
                      //     Text(
                      //       "Certificate: ",
                      //       style: textTheme.text14
                      //           ?.copyWith(fontWeight: FontWeight.w500),
                      //     ),
                      //     YBox(4),
                      //     Container(
                      //       padding: EdgeInsets.symmetric(
                      //         horizontal: Sizer.width(16),
                      //         vertical: Sizer.height(10),
                      //       ),
                      //       decoration: BoxDecoration(
                      //         border: Border.all(color: AppColors.neutral5),
                      //         borderRadius:
                      //             BorderRadius.circular(Sizer.radius(2)),
                      //       ),
                      //       child: Row(
                      //         children: [
                      //           SvgPicture.asset(
                      //             AppSvgs.iconAttachment,
                      //             height: Sizer.height(14),
                      //           ),
                      //           SizedBox(width: 8),
                      //           Expanded(
                      //             child: Text(
                      //               AppUtils.getDisplayFileName(
                      //                   widget.documents.cac?.file),
                      //               maxLines: 1,
                      //               overflow: TextOverflow.ellipsis,
                      //               style: textTheme.text14?.copyWith(
                      //                 color: colorScheme.primaryColor,
                      //               ),
                      //             ),
                      //           ),
                      //         ],
                      //       ),
                      //     ),
                      //   ],
                      // ),
                      YBox(16),
                      CustomTextField(
                        controller: tinNumberC,
                        labelText: 'TIN Number',
                        showLabelHeader: true,
                      ),
                      YBox(10),
                      UploadWidget(
                        labelText: 'Certificate:',
                        documentName: _tinUrl ?? _tinFile?.path.split('/').last,
                        isUploading:
                            ref.watch(fileUploadVm).busy("tinUploadState"),
                        onUpload: () async {
                          final file = await ImageAndDocUtils.pickDocument();
                          if (file != null) {
                            _tinFile = file;
                            final r = await ref.read(fileUploadVm).uploadFile(
                                file: [file], busyObjectName: "tinUploadState");
                            _tinUrl = r.data?.first.url;
                            setState(() {});
                          }
                        },
                        onRemove: () {
                          _tinFile = null;
                          _tinUrl = null;
                          setState(() {});
                        },
                      ),
                      YBox(20),
                      UploadWidget(
                        labelText: 'Proof of Address:',
                        documentName: _proofOfAddressUrl ??
                            _proofOfAddressFile?.path.split('/').last,
                        isUploading: ref
                            .watch(fileUploadVm)
                            .busy("proofOfAddressUploadState"),
                        onUpload: () async {
                          final file = await ImageAndDocUtils.pickDocument();
                          if (file != null) {
                            _proofOfAddressFile = file;
                            final r = await ref.read(fileUploadVm).uploadFile(
                                file: [file],
                                busyObjectName: "proofOfAddressUploadState");
                            _proofOfAddressUrl = r.data?.first.url;
                            setState(() {});
                          }
                        },
                        onRemove: () {
                          _proofOfAddressFile = null;
                          _proofOfAddressUrl = null;
                          setState(() {});
                        },
                      ),
                      YBox(30),
                      CustomBtn.solid(
                        text: "Submit",
                        onTap: () {
                          if (formKey.currentState?.validate() == true) {
                            _submitForm();
                          }
                        },
                      ),
                    ],
                  ),
                ),
              ],
            ),
          )),
    );
  }

  _submitForm() async {
    final vendorRef = ref.read(vendorProfileVmodel);

    // Create media list and filter out null/empty values
    List<Media> mediaList = [];

    mediaList.add(Media(
      name: "cac",
      url: _cacUrl ?? "",
      metadata: Iddata(identificationNumber: cacNumberC.text.trim()),
    ));

    // TIN media
    mediaList.add(Media(
      name: "tin",
      url: _tinUrl ?? "",
      metadata: Iddata(identificationNumber: tinNumberC.text.trim()),
    ));

    // Proof of Address media
    mediaList.add(Media(
      name: "proof_of_address",
      url: _proofOfAddressUrl ?? "",
    ));

    final res = await vendorRef.updateVendorProfile(
      VendorProfileParams(
        media: mediaList.isNotEmpty ? mediaList : null,
      ),
    );

    handleApiResponse(
      response: res,
      onSuccess: () {
        Navigator.pop(context);
        ref.read(vendorProfileVmodel).getVendorProfile();
      },
    );
  }
}
